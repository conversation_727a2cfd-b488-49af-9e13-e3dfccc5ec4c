using System;
using HutongGames.PlayMaker;
using UnityEngine;

namespace Fish.PlayMaker
{
	[ActionCategory("Fish/Common")]
	[HutongGames.PlayMaker.Tooltip("GAction实现")]
	public class GActionFsm : FsmStateAction
	{
		[RequiredField]
		[HutongGames.PlayMaker.Tooltip("目标对象")]
		public FsmOwnerDefault targetObject;
		public PMGAction Action;
		[HutongGames.PlayMaker.Tooltip("动作完成时触发的事件")]
		public FsmEvent finishedEvent;

		private GAction _currentAction;
		private GameObject _targetGameObject;

		public override void Reset()
		{
			targetObject = null;
			_currentAction = null;
			Action = new PMGAction();
		}

		public override void OnEnter()
		{
			_targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);

			if (_targetGameObject == null)
			{
				GameLogger.LogWarning("Target object is null!");
				Finish();
				return;
			}

			_currentAction = CreateAction(Action);

			if (_currentAction != null)
			{
				_currentAction.StartWithTarget(_targetGameObject);
				// 先更新一次，触发一下instant类型的action逻辑
				_currentAction.Step(0);

				// 这里需要再次检查是否为空，因为_currentAction.Step以后，如果Fsm的Owner被隐藏了，
				// 会走OnExit，在那里会清空_currentAction，所以这里需要再次判断
				if (_currentAction != null)
				{
					if (_currentAction.IsDone())
					{
						ActionFinished();
					}
				}
			}
			else
			{
				GameLogger.LogWarning("Failed to create GAction for type: " + Action.ActionType);
				Finish();
			}
		}

		public override void OnUpdate()
		{
			if (_currentAction != null)
			{
				_currentAction.Step(Time.deltaTime);

				if (_currentAction.IsDone())
				{
					ActionFinished();
				}
			}
		}

		private void ActionFinished()
		{
			if (finishedEvent != null)
			{
				Fsm.Event(finishedEvent);
			}
			if (_currentAction != null)
			{
				_currentAction.Stop();
			}
			_currentAction = null;

			Finish();
		}

		public override void OnExit()
		{
			if (_currentAction != null)
			{
				_currentAction.Stop();
				_currentAction = null;
			}
		}

		private GAction CreateAction(PMGAction action)
		{
			var type = action.ActionType;
			if (type == ActionType.Show)
			{
				return GAction.Show();
			}
			else if (type == ActionType.Hide)
			{
				return GAction.Hide();
			}
			else if (type == ActionType.RemoveSelf)
			{
				return GAction.RemoveSelf();
			}
			else if (type == ActionType.FlipX)
			{
				return GAction.FlipX();
			}
			else if (type == ActionType.FlipY)
			{
				return GAction.FlipY();
			}
			else if (type == ActionType.CallFunc)
			{
				// todo:	
			}
			else if (type == ActionType.DelayTime)
			{
				var param = action.ParamDelayTime;
				return GAction.DelayTime(param.duration.GetValue(_targetGameObject));
			}
			else if (type == ActionType.MoveBy)
			{
				var param = action.ParamMoveBy;
				return GAction.MoveBy(param.duration.GetValue(_targetGameObject), param.deltaPosition.GetValue(_targetGameObject), param.worldPosition.GetValue(_targetGameObject));
			}
			else if (type == ActionType.MoveTo)
			{
				var param = action.ParamMoveTo;
				return GAction.MoveTo(param.duration.GetValue(_targetGameObject), param.target.GetValue(_targetGameObject), param.worldPosition.GetValue(_targetGameObject));
			}
			else if (type == ActionType.RotateBy)
			{
				var param = action.ParamRotateBy;
				return GAction.RotateBy(param.duration.GetValue(_targetGameObject), param.deltaAngle.GetValue(_targetGameObject));
			}
			else if (type == ActionType.RotateTo)
			{
				var param = action.ParamRotateTo;
				return GAction.RotateTo(param.duration.GetValue(_targetGameObject), param.destAngle.GetValue(_targetGameObject));
			}
			else if (type == ActionType.RotateBy3D)
			{
				var param = action.ParamRotateBy3D;
				return GAction.RotateBy3d(param.duration.GetValue(_targetGameObject), param.deltaAngleX.GetValue(_targetGameObject), param.deltaAngleY.GetValue(_targetGameObject), param.deltaAngleZ.GetValue(_targetGameObject));
			}
			else if (type == ActionType.ScaleTo)
			{
				var param = action.ParamScaleTo;
				return GAction.ScaleTo(param.duration.GetValue(_targetGameObject), param.sx.GetValue(_targetGameObject), param.sy.GetValue(_targetGameObject), param.sz.GetValue(_targetGameObject));
			}
			else if (type == ActionType.ScaleBy)
			{
				var param = action.ParamScaleBy;
				return GAction.ScaleBy(param.duration.GetValue(_targetGameObject), param.sx.GetValue(_targetGameObject), param.sy.GetValue(_targetGameObject), param.sz.GetValue(_targetGameObject));
			}
			else if (type == ActionType.FadeTo)
			{
				var param = action.ParamFadeTo;
				return GAction.FadeTo(param.duration.GetValue(_targetGameObject), param.alpha.GetValue(_targetGameObject));
			}
			else if (type == ActionType.TintTo)
			{
				var param = action.ParamTintTo;
				return GAction.TintTo(param.duration.GetValue(_targetGameObject), param.r.GetValue(_targetGameObject), param.g.GetValue(_targetGameObject), param.b.GetValue(_targetGameObject));
			}
			else if (type == ActionType.BlendTo)
			{
				var param = action.ParamBlendTo;
				return GAction.BlendTo(param.duration.GetValue(_targetGameObject), param.a.GetValue(_targetGameObject));
			}
			else if (type == ActionType.Flash)
			{
				return GAction.Flash();
			}
			else if (type == ActionType.BezierTo)
			{
				var param = action.ParamBezierTo;
				return GAction.BezierTo(param.duration.GetValue(_targetGameObject), param.p0.GetValue(_targetGameObject), param.p1.GetValue(_targetGameObject), param.p2.GetValue(_targetGameObject), param.p3.GetValue(_targetGameObject));
			}
			else if(type == ActionType.CanvasGroupAlphaFadeTo)
			{
				var param = action.ParamCanvasGroupAlphaFadeTo;
				return GAction.CanvasGroupAlphaFadeTo(param.duration.GetValue(_targetGameObject), param.alpha.GetValue(_targetGameObject));
			}
			else if(type == ActionType.Repeat)
			{
				var param = action.ParamRepeate;
				if (param != null && param.action != null)
				{
					var subAction = CreateAction(param.action);
					if (subAction != null)
					{
						return GAction.Repeat(subAction, (uint)param.times.GetValue(_targetGameObject));
					}
				}
			}

			return null;
		}

		public enum ActionType
		{
			Show,
			Hide,
			RemoveSelf,
			FlipX,
			FlipY,
			CallFunc,// todo:
			DelayTime,
			MoveBy,
			MoveTo,
			RotateBy,
			RotateTo,
			RotateBy3D,
			ScaleTo,
			ScaleBy,
			FadeTo,
			TintTo,
			BlendTo,
			Flash,
			BezierTo,
			CanvasGroupAlphaFadeTo,
			Sequence,// todo:
			Spawn,// todo:
			Repeat,
		}
	}

	[Serializable]
	public class PMGAction
	{
		public GActionFsm.ActionType ActionType;
		[NonSerialized]
		public bool foldout = true;

		public PMGActionDelayTime ParamDelayTime;
		public PMGActionMoveBy ParamMoveBy;
		public PMGActionMoveTo ParamMoveTo;
		public PMGActionRotateBy ParamRotateBy;
		public PMGActionRotateTo ParamRotateTo;
		public PMGActionRotateBy3D ParamRotateBy3D;
		public PMGActionScaleTo ParamScaleTo;
		public PMGActionScaleBy ParamScaleBy;
		public PMGActionFadeTo ParamFadeTo;
		public PMGActionTintTo ParamTintTo;
		public PMGActionBlendTo ParamBlendTo;
		public PMGActionBezierTo ParamBezierTo;
		public PMGActionCanvasGroupAlphaFadeTo ParamCanvasGroupAlphaFadeTo;
		public PMGActionRepeate ParamRepeate;

		public void OnActionTypeChanged(GActionFsm.ActionType newActionType)
		{
			Reset();

			if (newActionType == GActionFsm.ActionType.DelayTime)
			{
				ParamDelayTime = new PMGActionDelayTime();
			}
			else if (newActionType == GActionFsm.ActionType.MoveBy)
			{
				ParamMoveBy = new PMGActionMoveBy();
			}
			else if (newActionType == GActionFsm.ActionType.MoveTo)
			{
				ParamMoveTo = new PMGActionMoveTo();
			}
			else if (newActionType == GActionFsm.ActionType.RotateBy)
			{
				ParamRotateBy = new PMGActionRotateBy();
			}
			else if (newActionType == GActionFsm.ActionType.RotateTo)
			{
				ParamRotateTo = new PMGActionRotateTo();
			}
			else if (newActionType == GActionFsm.ActionType.RotateBy3D)
			{
				ParamRotateBy3D = new PMGActionRotateBy3D();
			}
			else if (newActionType == GActionFsm.ActionType.ScaleTo)
			{
				ParamScaleTo = new PMGActionScaleTo();
			}
			else if (newActionType == GActionFsm.ActionType.ScaleBy)
			{
				ParamScaleBy = new PMGActionScaleBy();
			}
			else if (newActionType == GActionFsm.ActionType.FadeTo)
			{
				ParamFadeTo = new PMGActionFadeTo();
			}
			else if (newActionType == GActionFsm.ActionType.TintTo)
			{
				ParamTintTo = new PMGActionTintTo();
			}
			else if (newActionType == GActionFsm.ActionType.BlendTo)
			{
				ParamBlendTo = new PMGActionBlendTo();
			}
			else if (newActionType == GActionFsm.ActionType.BezierTo)
			{
				ParamBezierTo = new PMGActionBezierTo();
			}
			else if (newActionType == GActionFsm.ActionType.CanvasGroupAlphaFadeTo)
			{
				ParamCanvasGroupAlphaFadeTo = new PMGActionCanvasGroupAlphaFadeTo();
			}
			else if (newActionType == GActionFsm.ActionType.Repeat)
			{
				ParamRepeate = new PMGActionRepeate();
				ParamRepeate.
			}
			else if (newActionType == GActionFsm.ActionType.Sequence)
			{
			}
		}

		private void Reset()
		{
			ParamDelayTime = null;
		}
	}

	[Serializable]
	public class PMGActionDelayTime
	{
		public PMParam<float> duration;

		public PMGActionDelayTime()
		{
			duration = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionMoveBy
	{
		public PMParam<float> duration;
		public PMParam<Vector3> deltaPosition;
		public PMParam<bool> worldPosition;

		public PMGActionMoveBy()
		{
			duration = new PMParam<float>();
			deltaPosition = new PMParam<Vector3>();
			worldPosition = new PMParam<bool>();
		}
	}

	[Serializable]
	public class PMGActionMoveTo
	{
		public PMParam<float> duration;
		public PMParam<Vector3> target;
		public PMParam<bool> worldPosition;

		public PMGActionMoveTo()
		{
			duration = new PMParam<float>();
			target = new PMParam<Vector3>();
			worldPosition = new PMParam<bool>();
		}
	}

	[Serializable]
	public class PMGActionRotateBy
	{
		public PMParam<float> duration;
		public PMParam<float> deltaAngle;

		public PMGActionRotateBy()
		{
			duration = new PMParam<float>();
			deltaAngle = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionRotateTo
	{
		public PMParam<float> duration;
		public PMParam<float> destAngle;

		public PMGActionRotateTo()
		{
			duration = new PMParam<float>();
			destAngle = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionRotateBy3D
	{
		public PMParam<float> duration;
		public PMParam<float> deltaAngleX;
		public PMParam<float> deltaAngleY;
		public PMParam<float> deltaAngleZ;

		public PMGActionRotateBy3D()
		{
			duration = new PMParam<float>();
			deltaAngleX = new PMParam<float>();
			deltaAngleY = new PMParam<float>();
			deltaAngleZ = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionScaleTo
	{
		public PMParam<float> duration;
		public PMParam<float> sx;
		public PMParam<float> sy;
		public PMParam<float> sz;

		public PMGActionScaleTo()
		{
			duration = new PMParam<float>();
			sx = new PMParam<float>();
			sy = new PMParam<float>();
			sz = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionScaleBy
	{
		public PMParam<float> duration;
		public PMParam<float> sx;
		public PMParam<float> sy;
		public PMParam<float> sz;

		public PMGActionScaleBy()
		{
			duration = new PMParam<float>();
			sx = new PMParam<float>();
			sy = new PMParam<float>();
			sz = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionFadeTo
	{
		public PMParam<float> duration;
		public PMParam<float> alpha;

		public PMGActionFadeTo()
		{
			duration = new PMParam<float>();
			alpha = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionTintTo
	{
		public PMParam<float> duration;
		public PMParam<float> r;
		public PMParam<float> g;
		public PMParam<float> b;

		public PMGActionTintTo()
		{
			duration = new PMParam<float>();
			r = new PMParam<float>();
			g = new PMParam<float>();
			b = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionBlendTo
	{
		public PMParam<float> duration;
		public PMParam<float> a;
		public PMParam<float> r;
		public PMParam<float> g;
		public PMParam<float> b;

		public PMGActionBlendTo()
		{
			duration = new PMParam<float>();
			a = new PMParam<float>();
			r = new PMParam<float>();
			g = new PMParam<float>();
			b = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionBezierTo
	{
		public PMParam<float> duration;
		public PMParam<Vector3> p0;
		public PMParam<Vector3> p1;
		public PMParam<Vector3> p2;
		public PMParam<Vector3> p3;

		public PMGActionBezierTo()
		{
			duration = new PMParam<float>();
			p0 = new PMParam<Vector3>();
			p1 = new PMParam<Vector3>();
			p2 = new PMParam<Vector3>();
			p3 = new PMParam<Vector3>();
		}
	}

	[Serializable]
	public class PMGActionCanvasGroupAlphaFadeTo
	{
		public PMParam<float> duration;
		public PMParam<float> alpha;

		public PMGActionCanvasGroupAlphaFadeTo()
		{
			duration = new PMParam<float>();
			alpha = new PMParam<float>();
		}
	}

	[Serializable]
	public class PMGActionRepeate : ISerializationCallbackReceiver
	{
		// 运行时使用的action对象，不参与序列化
		[System.NonSerialized]
		public PMGAction action;

		public PMParam<int> times;

		// 用于序列化的字段
		[SerializeField]
		private GActionFsm.ActionType _serializedActionType;
		[SerializeField]
		private string _serializedActionData;

		// 序列化前调用 - 将action数据保存到序列化字段
		public void OnBeforeSerialize()
		{
			if (action != null)
			{
				_serializedActionType = action.ActionType;
				// 使用JsonUtility序列化action数据
				try
				{
					_serializedActionData = UnityEngine.JsonUtility.ToJson(action);
				}
				catch (System.Exception e)
				{
					UnityEngine.Debug.LogWarning($"Failed to serialize PMGAction: {e.Message}");
					_serializedActionData = "";
				}
			}
		}

		// 反序列化后调用 - 从序列化字段恢复action数据
		public void OnAfterDeserialize()
		{
			if (!string.IsNullOrEmpty(_serializedActionData))
			{
				try
				{
					action = UnityEngine.JsonUtility.FromJson<PMGAction>(_serializedActionData);
					if (action != null)
					{
						action.ActionType = _serializedActionType;
					}
				}
				catch (System.Exception e)
				{
					UnityEngine.Debug.LogWarning($"Failed to deserialize PMGAction: {e.Message}");
					action = new PMGAction();
				}
			}
			else
			{
				action = new PMGAction();
			}

			// 确保times不为null
			if (times == null)
			{
				times = new PMParam<int>();
				times.RawValue = 1;
			}
		}
	}

	[Serializable]
	public class PMGActionSequence
	{
		public PMGAction[] actions = new PMGAction[0];

		// 编辑器折叠状态（不序列化）
		[NonSerialized]
		public bool foldout = true;
	}


	[Serializable]
	public class PMParam<T>
	{
		public bool UseStoreData;
		public string StoreKey;
		public T RawValue;


		public T GetValue(GameObject gameObject)
		{
			if (UseStoreData)
			{
				var value = gameObject.GetDataObject(StoreKey);
				return (T)value;
			}
			else
			{
				return RawValue;
			}
		}
	}

}