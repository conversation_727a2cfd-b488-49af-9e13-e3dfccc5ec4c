using System;
using HutongGames.PlayMaker;
using UnityEngine;

namespace Fish.PlayMaker
{
	[ActionCategory("Fish/Common")]
	[HutongGames.PlayMaker.Tooltip("GAction实现")]
	public class GActionFsm : FsmStateAction
	{
		[RequiredField]
		[HutongGames.PlayMaker.Tooltip("目标对象")]
		public FsmOwnerDefault targetObject;
		public PMGAction Action;
		[HutongGames.PlayMaker.Tooltip("动作完成时触发的事件")]
		public FsmEvent finishedEvent;

		private GAction _currentAction;
		private GameObject _targetGameObject;

		public override void Reset()
		{
			targetObject = null;
			_currentAction = null;
			Action = new PMGAction();
		}

		public override void OnEnter()
		{
			_targetGameObject = Fsm.GetOwnerDefaultTarget(targetObject);

			if (_targetGameObject == null)
			{
				GameLogger.LogWarning("Target object is null!");
				Finish();
				return;
			}

			_currentAction = CreateAction(Action);

			if (_currentAction != null)
			{
				_currentAction.StartWithTarget(_targetGameObject);
				// 先更新一次，触发一下instant类型的action逻辑
				_currentAction.Step(0);

				// 这里需要再次检查是否为空，因为_currentAction.Step以后，如果Fsm的Owner被隐藏了，
				// 会走OnExit，在那里会清空_currentAction，所以这里需要再次判断
				if (_currentAction != null)
				{
					if (_currentAction.IsDone())
					{
						ActionFinished();
					}
				}
			}
			else
			{
				GameLogger.LogWarning("Failed to create GAction for type: " + Action.ActionType);
				Finish();
			}
		}

		public override void OnUpdate()
		{
			if (_currentAction != null)
			{
				_currentAction.Step(Time.deltaTime);

				if (_currentAction.IsDone())
				{
					ActionFinished();
				}
			}
		}

		private void ActionFinished()
		{
			if (finishedEvent != null)
			{
				Fsm.Event(finishedEvent);
			}
			if (_currentAction != null)
			{
				_currentAction.Stop();
			}
			_currentAction = null;

			Finish();
		}

		public override void OnExit()
		{
			if (_currentAction != null)
			{
				_currentAction.Stop();
				_currentAction = null;
			}
		}

		private GAction CreateAction(PMGAction action)
		{
			var type = action.ActionType;
			if (type == ActionType.Show)
			{
				return GAction.Show();
			}
			else if (type == ActionType.Hide)
			{
				return GAction.Hide();
			}
			else if (type == ActionType.RemoveSelf)
			{
				return GAction.RemoveSelf();
			}
			else if (type == ActionType.FlipX)
			{
				return GAction.FlipX();
			}
			else if (type == ActionType.FlipY)
			{
				return GAction.FlipY();
			}
			else if (type == ActionType.CallFunc)
			{
				// todo:	
			}
			else if (type == ActionType.DelayTime)
			{
				var param = action.Param as PMGActionDelayTime;
				return GAction.DelayTime(param.duration.GetValue(_targetGameObject));
			}

			return null;
		}

		public enum ActionType
		{
			Show,
			Hide,
			RemoveSelf,
			FlipX,
			FlipY,
			CallFunc,//todo
			DelayTime,
			MoveBy,
			MoveTo,
			RotateBy,
			RotateTo,
			RotateBy3D,
			ScaleTo,
			ScaleBy,
			FadeTo,
			TintTo,
			BlendTo,
			Flash,
			BezierTo,
			CanvasGroupAlphaFadeTo,
			Sequence,
		}
	}

	[Serializable]
	public class PMGAction
	{
		public GActionFsm.ActionType ActionType;
		public PMGActionBase Param;

		[System.NonSerialized]
		public bool foldout = true;

		public void OnActionTypeChanged(GActionFsm.ActionType newActionType)
		{
			if (newActionType == GActionFsm.ActionType.DelayTime)
			{
				Param = new PMGActionDelayTime();
			}
			else if (newActionType == GActionFsm.ActionType.MoveTo)
			{
				Param = new PMGActionMoveTo();
			}
			else if (newActionType == GActionFsm.ActionType.Sequence)
			{
				Param = new PMGActionSequence();
			}
		}

		
	}

	[Serializable]
	public class PMGActionBase
	{
	}

	[Serializable]
	public class PMGActionDelayTime : PMGActionBase
	{
		public PMParam<float> duration;

		public PMGActionDelayTime()
		{
			duration = new PMParam<float>();
		 }
	}

	[Serializable]
	public class PMGActionSequence : PMGActionBase
	{
		public PMGAction[] actions = new PMGAction[0];

		// 编辑器折叠状态（不序列化）
		[NonSerialized]
		public bool foldout = true;
	}

	[Serializable]
	public class PMGActionMoveTo : PMGActionBase
	{
		public PMParam<float> duration;
		public PMParam<Vector3> target;

		public PMGActionMoveTo()
		{
			duration = new PMParam<float>();
			target = new PMParam<Vector3>();
		}
	}

	[Serializable]
	public class PMParam<T>
	{
		public bool UseStoreData;
		public string StoreKey;
		public T RawValue;


		public T GetValue(GameObject gameObject)
		{
			if (UseStoreData)
			{
				var value = gameObject.GetDataObject(StoreKey);
				return (T)value;
			}
			else
			{
				return RawValue;
			}
		}
	}

}