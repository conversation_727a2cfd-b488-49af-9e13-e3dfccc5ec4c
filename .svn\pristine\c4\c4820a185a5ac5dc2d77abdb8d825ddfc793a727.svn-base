using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;

namespace Fish.PlayMakerEditor
{
	[CustomActionEditor(typeof(GActionFsm))]
	public class GActionUniversalFsmEditor : CustomActionEditor
	{
		public override bool OnGUI()
		{
			var action = target as GActionFsm;
			EditField("targetObject");

			EditorGUILayout.Space();

			// 使用递归方法显示子动作属性
			DrawSubActionProperties(action.Action, 0);

			return GUI.changed;
		}

		private void DrawSubActionProperties(PMGAction subAction, int depth)
		{
			// 显示动作类型选择
			GActionFsm.ActionType newActionType = (GActionFsm.ActionType)EditorGUILayout.EnumPopup("Type", subAction.ActionType);
			if (newActionType != subAction.ActionType)
			{
				subAction.ActionType = newActionType;
				subAction.OnActionTypeChanged(newActionType);
			}

			// 根据类型显示对应的属性
			switch (subAction.ActionType)
			{
				case GActionFsm.ActionType.Show:
					break;
				case GActionFsm.ActionType.Hide:
					break;
				case GActionFsm.ActionType.RemoveSelf:
					break;
				case GActionFsm.ActionType.FlipX:
					break;
				case GActionFsm.ActionType.FlipY:
					break;

				case GActionFsm.ActionType.CallFunc:
					// todo:
					break;
				case GActionFsm.ActionType.DelayTime:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = subAction.ParamDelayTime;

						DrawParam("Duration", param.duration);
					}
					break;
				case GActionFsm.ActionType.MoveBy:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = subAction.ParamMoveBy;

						DrawParam("Duration", param.duration);
						DrawParam("DeltaPosition", param.deltaPosition);
						DrawParam("WorldPosition", param.worldPosition);
					}
					break;

				case GActionFsm.ActionType.MoveTo:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);

						var param = subAction.ParamMoveTo;

						DrawParam("Duration", param.duration);
						DrawParam("Target", param.target);
						DrawParam("WorldPosition", param.worldPosition);
					}

					break;
				case GActionFsm.ActionType.RotateBy:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = subAction.ParamRotateBy;

						DrawParam("Duration", param.duration);
						DrawParam("DeltaAngle", param.deltaAngle);
					}
					break;
				case GActionFsm.ActionType.RotateTo:
					{
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);
						var param = subAction.ParamRotateTo;

						DrawParam("Duration", param.duration);
						DrawParam("DestAngle", param.destAngle);
					}
					break;
				case GActionFsm.ActionType.Sequence:
					{
						// var param = subAction.Param as PMGActionSequence;
						// // 添加Sequence的折叠功能
						// param.foldout = EditorGUILayout.Foldout(param.foldout, "Settings", true, EditorStyles.foldoutHeader);

						// if (param.foldout)
						// {
						// 	EditorGUI.indentLevel++;

						// 	// 显示数组大小控制
						// 	int newSize = EditorGUILayout.IntField("Count", param.actions.Length);
						// 	if (newSize != param.actions.Length)
						// 	{
						// 		System.Array.Resize(ref param.actions, newSize);
						// 		for (int i = 0; i < param.actions.Length; i++)
						// 		{
						// 			if (param.actions[i] == null)
						// 			{
						// 				param.actions[i] = new PMGAction();
						// 			}
						// 		}
						// 	}

						// 	EditorGUILayout.Space();

						// 	// 递归显示每个子动作的配置
						// 	for (int i = 0; i < param.actions.Length; i++)
						// 	{
						// 		// 为嵌套的子动作创建更深的背景色，并显示深度信息
						// 		var bgColor = depth % 2 == 0 ? "box" : "helpbox";
						// 		EditorGUILayout.BeginVertical(bgColor);

						// 		if (param.actions[i] == null)
						// 		{
						// 			param.actions[i] = new PMGAction();
						// 		}

						// 		var nestedSubAction = param.actions[i];

						// 		// 添加嵌套子动作的折叠功能，显示深度信息
						// 		EditorGUILayout.BeginHorizontal();
						// 		string foldoutLabel = $"Action {i}";
						// 		nestedSubAction.foldout = EditorGUILayout.Foldout(nestedSubAction.foldout, foldoutLabel, true, EditorStyles.foldout);
						// 		EditorGUILayout.EndHorizontal();

						// 		if (nestedSubAction.foldout)
						// 		{
						// 			EditorGUILayout.Space();

						// 			// 递归显示属性
						// 			DrawSubActionProperties(nestedSubAction, depth + 1);
						// 		}

						// 		EditorGUILayout.EndVertical();
						// 		EditorGUILayout.Space();
						// 	}
						// }
					}
					break;
			}
		}

		private void DrawParam<T>(string fieldName, PMParam<T> param)
		{
			EditorGUILayout.LabelField(fieldName, EditorStyles.boldLabel);

			param.UseStoreData = EditorGUILayout.Toggle("UseStoreData", param.UseStoreData);

			if (param.UseStoreData)
			{
				param.StoreKey = EditorGUILayout.TextField("StoreKey", param.StoreKey);
			}
			else
			{
				if (typeof(T) == typeof(string))
				{
					param.RawValue = (T)(object)EditorGUILayout.TextField("Value", (string)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(int))
				{
					param.RawValue = (T)(object)EditorGUILayout.IntField("Value", (int)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(long))
				{
					param.RawValue = (T)(object)EditorGUILayout.LongField("Value", (long)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(float))
				{
					param.RawValue = (T)(object)EditorGUILayout.FloatField("Value", (float)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(double))
				{
					param.RawValue = (T)(object)EditorGUILayout.DoubleField("Value", (double)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(bool))
				{
					param.RawValue = (T)(object)EditorGUILayout.Toggle("Value", (bool)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Vector2))
				{
					param.RawValue = (T)(object)EditorGUILayout.Vector2Field("Value", (Vector2)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Vector3))
				{
					param.RawValue = (T)(object)EditorGUILayout.Vector3Field("Value", (Vector3)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Vector4))
				{
					param.RawValue = (T)(object)EditorGUILayout.Vector4Field("Value", (Vector4)(object)param.RawValue);
				}
				else if (typeof(T) == typeof(Color))
				{
					param.RawValue = (T)(object)EditorGUILayout.ColorField("Value", (Color)(object)param.RawValue);
				}
				else
				{
					EditorGUILayout.LabelField("不支持的类型");
				}
			}

			EditorGUILayout.Space();
		}
	}
}