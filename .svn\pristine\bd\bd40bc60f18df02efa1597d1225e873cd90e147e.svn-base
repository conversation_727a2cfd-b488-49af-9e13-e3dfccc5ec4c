using UnityEditor;
using HutongGames.PlayMakerEditor;
using Fish.PlayMaker;
using UnityEngine;

namespace Fish.PlayMakerEditor
{
	[CustomActionEditor(typeof(GActionFsm))]
	public class GActionUniversalFsmEditor : CustomActionEditor
	{
		public override bool OnGUI()
		{
			var action = target as GActionFsm;
			EditField("targetObject");

			EditorGUILayout.Space();

			// 使用递归方法显示子动作属性
			DrawSubActionProperties(action.Action, 0);

			return GUI.changed;
		}

		private void DrawSubActionProperties(PMGAction subAction, int depth)
		{
			// 显示动作类型选择
			GActionFsm.ActionType newActionType = (GActionFsm.ActionType)EditorGUILayout.EnumPopup("Type", subAction.ActionType);
			if (newActionType != subAction.ActionType)
			{
				subAction.ActionType = newActionType;
				subAction.OnActionTypeChanged(newActionType);
			}

			// 根据类型显示对应的属性
			switch (subAction.ActionType)
			{
				case GActionFsm.ActionType.MoveTo:
					{
						// MoveTo属性始终显示，不需要折叠（属性简单）
						EditorGUILayout.LabelField("Settings", EditorStyles.boldLabel);

						var action = subAction.Action as PMGActionMoveTo;

						// Duration字段
						DrawParam("Duration", action.duration);
						DrawParam("Target", action.target);
					}

					break;

				case GActionFsm.ActionType.Hide:
					break;

				case GActionFsm.ActionType.Sequence:
					{
						var action = subAction.Action as PMGActionSequence;
						// 添加Sequence的折叠功能
						action.foldout = EditorGUILayout.Foldout(action.foldout, "Settings", true, EditorStyles.foldoutHeader);

						if (action.foldout)
						{
							EditorGUI.indentLevel++;

							// 显示数组大小控制
							int newSize = EditorGUILayout.IntField("Count", action.actions.Length);
							if (newSize != action.actions.Length)
							{
								System.Array.Resize(ref action.actions, newSize);
								for (int i = 0; i < action.actions.Length; i++)
								{
									if (action.actions[i] == null)
									{
										action.actions[i] = new PMGAction();
									}
								}
							}

							EditorGUILayout.Space();

							// 递归显示每个子动作的配置
							for (int i = 0; i < action.actions.Length; i++)
							{
								// 为嵌套的子动作创建更深的背景色，并显示深度信息
								var bgColor = depth % 2 == 0 ? "box" : "helpbox";
								EditorGUILayout.BeginVertical(bgColor);

								if (action.actions[i] == null)
								{
									action.actions[i] = new PMGAction();
								}

								var nestedSubAction = action.actions[i];

								// 添加嵌套子动作的折叠功能，显示深度信息
								EditorGUILayout.BeginHorizontal();
								string foldoutLabel = $"Action {i}";
								nestedSubAction.foldout = EditorGUILayout.Foldout(nestedSubAction.foldout, foldoutLabel, true, EditorStyles.foldout);
								EditorGUILayout.EndHorizontal();

								if (nestedSubAction.foldout)
								{
									EditorGUILayout.Space();

									// 递归显示属性
									DrawSubActionProperties(nestedSubAction, depth + 1);
								}

								EditorGUILayout.EndVertical();
								EditorGUILayout.Space();
							}
						}
					}
					break;
			}
		}

		private void DrawParam<T>(string fieldName, PMParam<T> param)
		{
			EditorGUILayout.LabelField(fieldName, EditorStyles.boldLabel);

			// 显示UseStoreData复选框，使用更简洁的布局
			param.UseStoreData = EditorGUILayout.Toggle("UseStoreData", param.UseStoreData);

			// 根据UseStoreData状态显示不同的输入字段
			if (param.UseStoreData)
			{
				param.StoreKey = EditorGUILayout.TextField("StoreKey", param.StoreKey);
			}
			else
			{
				if (typeof(T) == typeof(float))
				{
					param.Value = (T)(object)EditorGUILayout.FloatField("Value", (float)(object)param.Value);
				}
				else if (typeof(T) == typeof(Vector3))
				{
					param.Value = (T)(object)EditorGUILayout.Vector3Field("Value", (Vector3)(object)param.Value);
				}
				else if (typeof(T) == typeof(Color))
				{
					param.Value = (T)(object)EditorGUILayout.ColorField("Value", (Color)(object)param.Value);
				}
				else
				{
					EditorGUILayout.LabelField("不支持的类型");
				}
			}

			EditorGUILayout.Space();
		}
	}
}