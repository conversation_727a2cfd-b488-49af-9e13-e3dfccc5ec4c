<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="eceea3cd-5c9e-4293-8bfd-92c8f174711d" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/Assets/Effect/material/Material_Fish/fx_mat_15019_zidan_glow05.mat.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Effect/material/Material_Fish/fx_mat_15019_zidan_glow05.mat.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/zhanshen_duotianshi_sortie_skin.fbx.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/Cannon/PrefabArt3D/cannon_DuoTianShi/Model/zhanshen_duotianshi_sortie_skin.fbx.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/zhanshen_hadisi_sortie_skin.fbx.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/Cannon/PrefabArt3D/cannon_HaDiShi/Model/zhanshen_hadisi_sortie_skin.fbx.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/CannonSkin/PrefabArt/EMo/model/fish_1665_emo_skin.fbx.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/CannonSkin/PrefabArt/EMo/model/fish_1665_emo_skin.fbx.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Materials/Custom_Cartoon_skin.mat.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Materials/Custom_Cartoon_skin.mat.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Materials/Custom_Skin.mat.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Materials/Custom_Skin.mat.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi.FBX.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi.FBX.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi_skin.FBX.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/CannonSkin/PrefabArt/Tanatuosi/Model/zhanshen_tanatuosi_skin.FBX.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1416/Materials/fish_1416_longhunzhu_diff001.mat" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1416/Materials/fish_1416_longhunzhu_diff001.mat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1634/Model/fish_1634_yanhou_baozuo_skin.fbx.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1634/Model/fish_1634_yanhou_baozuo_skin.fbx.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1634/Model/fish_1634_yanhou_skin.fbx.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1634/Model/fish_1634_yanhou_skin.fbx.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1651/Model/fish_1651_xiongmaocaishen_skin.fbx.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1651/Model/fish_1651_xiongmaocaishen_skin.fbx.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1663/Materials/CrackMask.mat.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1663/Materials/CrackMask.mat.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1677/model/fish_1677_shatanpaiqiu_A1_skin.fbx.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1677/model/fish_1677_shatanpaiqiu_A1_skin.fbx.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1677/model/fish_1677_shatanpaiqiu_A2_skin.fbx.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1677/model/fish_1677_shatanpaiqiu_A2_skin.fbx.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1677/model/fish_1677_shatanpaiqiu_B1_skin.fbx.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1677/model/fish_1677_shatanpaiqiu_B1_skin.fbx.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1677/model/fish_1677_shatanpaiqiu_B2_skin.fbx.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1677/model/fish_1677_shatanpaiqiu_B2_skin.fbx.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1681/Model/boss_1681_jxs_skin.FBX.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1681/Model/boss_1681_jxs_skin.FBX.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1682/model/boss_1682_binggui_skin.FBX.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1682/model/boss_1682_binggui_skin.FBX.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1751/model/fish_1751_anxingjuxiezuo_xingqiu_skin.FBX.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fishes/FishBoss/PrefabArt/1751/model/fish_1751_anxingjuxiezuo_xingqiu_skin.FBX.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GameRes/UIPanel/PrefabArt/sds_baoku/material/mat_sds_baoku_main_stage_task_kuang.mat.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GameRes/UIPanel/PrefabArt/sds_baoku/material/mat_sds_baoku_main_stage_task_kuang.mat.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GameScript/Editor/PlayMaker/GActionFsmEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GameScript/Editor/PlayMaker/GActionFsmEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GameScript/Runtime/playmaker/common/GActionFsm.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GameScript/Runtime/playmaker/common/GActionFsm.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/PlayMaker/Editor/Resources/EditorStartupPrefs.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/PlayMaker/Editor/Resources/EditorStartupPrefs.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Plugins/FMOD/addons/ResonanceAudio/Editor/FMODUnityResonanceEditor.asmdef" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Plugins/FMOD/addons/ResonanceAudio/Editor/FMODUnityResonanceEditor.asmdef" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/[废弃]/TA_Test/yuwei/juxie/anxingjuxie_yifu_effect 1.mat.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/[废弃]/TA_Test/yuwei/juxie/anxingjuxie_yifu_effect 1.mat.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Packages/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/Packages/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Packages/packages-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/Packages/packages-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/ProjectSettings.asset" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/ProjectSettings.asset" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DpaMonitoringSettings">
    <option name="firstShow" value="false" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/3e401efa0ce4422582abd640a807e26176f000/31/b4102d66/AssetDatabase.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/3e401efa0ce4422582abd640a807e26176f000/4d/2c5c234f/EditorSceneManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/3e401efa0ce4422582abd640a807e26176f000/a5/f3ab8ad4/MessageType.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/62/3002f466/FsmStateAction.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/6f/b88332a3/FsmArray.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/7c/beedb89a/ActionCategoryAttribute.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/98/1e81a35c/FsmState.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/99/f5c9f869/ActionData.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/c2/53d1e81a/Fsm.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/c4/34786eb6/CompoundArrayAttribute.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/e2/3bcb688f/PlayMakerFSM.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/e6/123fab32/ActionCategory.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/ed/dc1fc009/FsmEnum.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/fa/8b6611c6/FsmGameObject.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5b31c0e3f019417b9b9a403ad5bd3aae38200/fa/98f3f321/FsmString.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/695d1cc93cca45069c528c15c9fdd7493e2800/59/956c8028/Single.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/695d1cc93cca45069c528c15c9fdd7493e2800/5b/6bea81b6/Dictionary`2.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/695d1cc93cca45069c528c15c9fdd7493e2800/7a/f7d87ca5/AutoResetEvent.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/695d1cc93cca45069c528c15c9fdd7493e2800/c2/e9b8db03/KeyValuePair`2.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6b1444477c1d41c692ef6c02a838308529ac30/10/54abc1c5/Assembly.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6b1444477c1d41c692ef6c02a838308529ac30/70/530323d9/DateTime.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/6b1444477c1d41c692ef6c02a838308529ac30/f8/72ec6976/Void.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/7b67ea7a31af4ddfa668bdaf9d8bf0c8c5600/14/c5917866/VisualElement.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/7b67ea7a31af4ddfa668bdaf9d8bf0c8c5600/5e/d2d3fd91/TextElement.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/7b67ea7a31af4ddfa668bdaf9d8bf0c8c5600/dc/bd70c56b/Label.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/913bb12059fd4cef8da5cc94ad2f093313e400/39/89ccc94d/Vector2.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/913bb12059fd4cef8da5cc94ad2f093313e400/79/77289118/GL.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/913bb12059fd4cef8da5cc94ad2f093313e400/da/49fd5e04/GameObject.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/cc413f89440a4cada8bab7c7a4014b1215e000/fa/dc10ce1a/CustomActionEditor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/GameScript/Runtime/playmaker/common/CallCLRMethodAction.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/GameScript/Runtime/playmaker/common/GActionPlayMaker.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/GameScript/Runtime/ui/UIBg.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ide.rider@3.0.36/Rider/Editor/RiderScriptEditorDataPersisted.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.test-framework@1.1.33/UnityEditor.TestRunner/NUnitExtension/Attributes/TestPlayerBuildModifierAttribute.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.test-framework@1.1.33/UnityEngine.TestRunner/NUnitExtensions/Commands/EnumerableRepeatedTestCommand.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventTrigger.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui@1.0.0/Runtime/EventSystem/EventTriggerType.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui@1.0.0/Runtime/UI/Core/Button.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui@1.0.0/Runtime/UI/Core/ScrollRect.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2xlcmZy95T5iHwGbtasNotdBRCC" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Attach to Unity Editor.Attach to Unity Editor.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.lookFeel&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Attach to Unity Editor.Attach to Unity Editor">
    <configuration name="Start Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath C:\Users\<USER>\Documents\game\client -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="C:\Users\<USER>\Documents\game\client" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Unit Tests (batch mode)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\Hub\Editor\2020.3.47f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath C:\Users\<USER>\Documents\game\client -testResults Logs/results.xml -logFile Logs/Editor.log -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="C:\Users\<USER>\Documents\game\client" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor &amp; Play" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="UNITY_ATTACH_AND_PLAY" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="runtimes">
        <list />
      </option>
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Users\<USER>\Documents\game\client" />
          <option name="myCopyRoot" value="C:\Users\<USER>\Documents\game\client" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Users\<USER>\Documents\game\client" />
          <option name="myCopyRoot" value="C:\Users\<USER>\Documents\game\client\fish3d_game" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Users\<USER>\Documents\game\client" />
          <option name="myCopyRoot" value="C:\Users\<USER>\Documents\game\client" />
        </SvnCopyRootSimple>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Users\<USER>\Documents\game\client" />
          <option name="myCopyRoot" value="C:\Users\<USER>\Documents\game\client\fish3d_game" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="eceea3cd-5c9e-4293-8bfd-92c8f174711d" name="Changes" comment="" />
      <created>1748516261783</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748516261783</updated>
      <workItem from="1748516263676" duration="131000" />
      <workItem from="1748516408755" duration="128000" />
      <workItem from="1748516670997" duration="1258000" />
      <workItem from="1748575689378" duration="4355000" />
      <workItem from="1748932475783" duration="147000" />
      <workItem from="1748932635519" duration="32000" />
      <workItem from="1748934568322" duration="25712000" />
      <workItem from="1749033876046" duration="856000" />
      <workItem from="1749103965227" duration="16000" />
      <workItem from="1749103992335" duration="308000" />
      <workItem from="1749104315808" duration="8180000" />
      <workItem from="1749283230948" duration="724000" />
      <workItem from="1749286188086" duration="663000" />
      <workItem from="1749436518494" duration="11335000" />
      <workItem from="1749548210482" duration="5084000" />
      <workItem from="1749604339075" duration="13552000" />
      <workItem from="1749637882608" duration="1374000" />
      <workItem from="1749718457077" duration="512000" />
      <workItem from="1749722822872" duration="2827000" />
      <workItem from="1749726103866" duration="2304000" />
      <workItem from="1749786045043" duration="243000" />
      <workItem from="1749796021953" duration="2264000" />
      <workItem from="1749802606508" duration="606000" />
      <workItem from="1749806592520" duration="651000" />
      <workItem from="1750045779587" duration="2998000" />
      <workItem from="1750069021093" duration="981000" />
      <workItem from="1750123640383" duration="88000" />
      <workItem from="1750124282107" duration="78000" />
      <workItem from="1750140917959" duration="151000" />
      <workItem from="1750141178540" duration="2000" />
      <workItem from="1750141925143" duration="894000" />
      <workItem from="1750143024378" duration="200000" />
      <workItem from="1750143373054" duration="136000" />
      <workItem from="1750143532355" duration="81000" />
      <workItem from="1750143632573" duration="5000" />
      <workItem from="1750143656918" duration="11598000" />
      <workItem from="1750158164449" duration="1562000" />
      <workItem from="1750215137267" duration="21000" />
      <workItem from="1750215207005" duration="707000" />
      <workItem from="1750217199414" duration="268000" />
      <workItem from="1750217475898" duration="14000" />
      <workItem from="1750217497120" duration="226000" />
      <workItem from="1750233349604" duration="72000" />
      <workItem from="1750233586639" duration="599000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="true" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
    <option name="unityProject" value="true" />
    <option name="unityProjectFolder" value="true" />
  </component>
  <component name="UnityUnitTestConfiguration" currentTestLauncher="Both" />
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
</project>